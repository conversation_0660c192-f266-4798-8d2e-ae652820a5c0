
**AutoMes API Platform**


**简介**：<p>AutoMes Help doc</p>


**HOST**:***************:31201

**联系人**:<PERSON>

**Version**:1.0.0

**接口路径**：/v2/api-docs?group=订单接口


# 团装/高定单接口
## 团装下单


**接口描述**:


**接口地址**:`/out/pp/create/model3/{token}`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`


**请求示例**：
```json
{
	"customerId": "SP-030239",
	"customerName": "美国HM加州",
	"deliveryAddress": "美国HM加州",
	"deliveryCountry": "美国",
	"deliveryTel": "08-76321111",
	"devliveryWay": "海运",
	"dueDate": "2024-10-23",
	"lineList": [
		{
			"bomList": [
				{
					"cut_width": 11,
					"cut_wstrat": 20,
					"mainMterial": false,
					"materialBatchno": "PC-2024-312",
					"materialClrnm": "红色03",
					"materialColor": "HD-CC-03",
					"materialCom": "12织",
					"materialId": "DT-34003",
					"materialName": "DT-34003",
					"materialTypeId": "JND-023",
					"materialTypeName": "真织面料",
					"materialUsage": "包装",
					"thirdUid": "012c3812-2e3b-4f5a-bffa-cce5180c8178",
					"tolConsu": 123.21,
					"uniConsu": 0.47,
					"unitcd": "kg",
					"unitnm": "千克",
					"width": 1.47
				}
			],
			"finishedSize": "158x123",
			"itemList": [
				{
					"cusadr": "无锡电信港下分局",
					"cushgt": 178.4,
					"cusicd": "8dc9d027-5465-4f07-917f-9d5391bf848a",
					"cusnam": "李嘉",
					"cusncd": "CX-U0023",
					"cussex": 1,
					"custel": 1380000000,
					"cuswgt": 74,
					"deptnm": "无锡电信",
					"itemQty": 23,
					"msutnm": "李经理",
					"sizeNo": "XL",
					"surList": [
						{
							"msuCode": "胸围(XW)",
							"msuType": "套号(TH)",
							"msuVal": 78.8
						}
					]
				}
			],
			"mainMcolor": "藏青-31",
			"mainMid": "QZ-230231",
			"mainMname": "QZ-230231",
			"mainSpec": "DG-0231",
			"prodId": "FZ-NDQ",
			"prodName": "女短裙",
			"progtype": "春秋-34003",
			"remark": "藏青-31",
			"skuName": "春秋-34003",
			"skuSpec": "春秋-34003",
			"stylId": "ST-春季-02",
			"stylName": "ST-春季-02",
			"tecList": [
				{
					"craftdetail": "",
					"img1Name": "",
					"img1Url": "http://**/doc/tec/file",
					"img2Name": "",
					"img2Url": "http://**/doc/tec/file",
					"img3Name": "",
					"img3Url": "http://**/doc/tec/file",
					"pdf1Name": "",
					"pdf1Url": "http://**/doc/tec/file",
					"pdf2Name": "",
					"pdf2Url": "http://**/doc/tec/file",
					"pdf3Name": "",
					"pdf3Url": "http://**/doc/tec/file",
					"pdptcd": "",
					"pdptnm": "",
					"wctycd": "",
					"wctynm": ""
				}
			],
			"unitnm": "件"
		}
	],
	"mrchds": "李嘉",
	"orderDate": "2024-10-23",
	"orderNo": "SO-0023XF",
	"orderPno": "PO-0023XF",
	"orderTotal": 23423,
	"ppFrom": "ERP",
	"remark": "在ST-春季-01款上，口袋向下0.5cm",
	"thirdUid": "8dc9d027-5465-4f07-917f-9d5391bf848a",
	"wkspcd": "李嘉"
}
```


**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|modelReqeust| modelReqeust  | body | true |国内团装下单请求  | 国内团装下单请求   |
|status| 订单状态(新单|审核通过|产前完成)  | query | false |string  |    |
|token| 请求令牌  | path | true |string  |    |

**schema属性说明**



**国内团装下单请求**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|customerId| 客户编号  | body | false |string  |    |
|customerName| 客户名称  | body | false |string  |    |
|deliveryAddress| 发货地址  | body | false |string  |    |
|deliveryCountry| 发货国家  | body | false |string  |    |
|deliveryTel| 发货联系  | body | false |string  |    |
|devliveryWay| 发货方式  | body | false |string  |    |
|dueDate| 交货日期  | body | true |string  |    |
|lineList| 订单明细  | body | true |array  | 国内团装订单明细   |
|mrchds| 业务员  | body | false |string  |    |
|orderDate| 下单日期  | body | false |string  |    |
|orderNo| 销售订单号  | body | false |string  |    |
|orderPno| 生产计划号  | body | true |string  |    |
|orderTotal| 订单数量  | body | false |integer(int32)  |    |
|ppFrom| 订单来源系统  | body | true |string  |    |
|remark| 订单备注  | body | false |string  |    |
|thirdUid| 订单唯一主键(来源系统)  | body | true |string  |    |
|wkspcd| 指定车间  | body | false |string  |    |

**国内团装订单明细**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|bomList| 物料清单  | body | false |array  | 外贸大货物料清单   |
|finishedSize| 成品尺寸  | body | false |string  |    |
|itemList| 尺码清单  | body | true |array  | 国内团装尺码清单   |
|mainMcolor| 主料(面料)颜色  | body | false |string  |    |
|mainMid| 主料(面料)编号  | body | true |string  |    |
|mainMname| 主料(面料)名称  | body | false |string  |    |
|mainSpec| 主料(面料)规格  | body | false |string  |    |
|prodId| 品类编号  | body | true |string  |    |
|prodName| 品类名称  | body | false |string  |    |
|progtype| 产品类型  | body | false |string  |    |
|remark| 备注  | body | false |string  |    |
|skuName| 产品名称  | body | false |string  |    |
|skuSpec| 产品规格  | body | false |string  |    |
|stylId| 款式编号  | body | true |string  |    |
|stylName| 款式名称  | body | false |string  |    |
|tecList| 工艺清单  | body | false |array  | 工艺文件   |
|unitnm| 单位  | body | false |string  |    |

**外贸大货物料清单**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|cut_width| 裁剪宽度  | body | false |string  |    |
|cut_wstrat| 裁剪用量  | body | false |string  |    |
|mainMterial| 主料(非常备)/辅料(常备)  | body | true |boolean  |    |
|materialBatchno| 物料批次  | body | false |string  |    |
|materialClrnm| 物料颜色名称  | body | false |string  |    |
|materialColor| 物料颜色编号  | body | false |string  |    |
|materialCom| 物料成份  | body | false |string  |    |
|materialId| 物料编号  | body | true |string  |    |
|materialName| 物料名称  | body | false |string  |    |
|materialTypeId| 物料分类编号  | body | false |string  |    |
|materialTypeName| 物料分类名称  | body | false |string  |    |
|materialUsage| 物料用途  | body | false |string  |    |
|thirdUid| 每三方系统唯一主键  | body | false |string  |    |
|tolConsu| 总用量  | body | false |number  |    |
|uniConsu| 物料单耗  | body | false |number  |    |
|unitcd| 单位编号  | body | false |string  |    |
|unitnm| 单位名称  | body | false |string  |    |
|width| 物料宽度  | body | false |number  |    |

**国内团装尺码清单**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|cusadr| 消费者地址  | body | false |string  |    |
|cushgt| 消费者身高  | body | false |string  |    |
|cusicd| 消费者加款唯一ID  | body | false |string  |    |
|cusnam| 消费者名称  | body | false |string  |    |
|cusncd| 消费者编号  | body | false |string  |    |
|cussex| 消费者性别(1男，0女)  | body | false |string  |    |
|custel| 消费者电话  | body | false |string  |    |
|cuswgt| 消费者体重  | body | false |string  |    |
|deptnm| 消费者部门  | body | false |string  |    |
|itemQty| 数量  | body | true |integer(int32)  |    |
|msutnm| 量体师名称  | body | false |string  |    |
|sizeNo| 尺码  | body | true |string  |    |
|surList| 量体明细  | body | false |array  | 量体明细   |

**量体明细**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|msuCode| 量体编码  | body | true |string  |    |
|msuType| 量体方式  | body | true |string  |    |
|msuVal| 量体结果  | body | true |string  |    |

**工艺文件**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|craftdetail| 工艺描述  | body | false |string  |    |
|img1Name| 工艺图1名称  | body | false |string  |    |
|img1Url| 工艺图1下载地址  | body | false |string  |    |
|img2Name| 工艺图2名称  | body | false |string  |    |
|img2Url| 工艺图2下载地址  | body | false |string  |    |
|img3Name| 工艺图3名称  | body | false |string  |    |
|img3Url| 工艺图3下载地址  | body | false |string  |    |
|pdf1Name| 工艺文件1名称  | body | false |string  |    |
|pdf1Url| 工艺文件1下载地址  | body | false |string  |    |
|pdf2Name| 工艺文件2名称  | body | false |string  |    |
|pdf2Url| 工艺文件2下载地址  | body | false |string  |    |
|pdf3Name| 工艺文件3名称  | body | false |string  |    |
|pdf3Url| 工艺文件3下载地址  | body | false |string  |    |
|pdptcd| 部件编号  | body | false |string  |    |
|pdptnm| 部件名称  | body | false |string  |    |
|wctycd| 工段编号  | body | false |string  |    |
|wctynm| 工段名称  | body | false |string  |    |

**响应示例**:

```json
{
	"txt": "",
	"st": "0成功，-1失败",
	"msg": "Success",
	"body": {
		"pplanno": "PP24060078"
	}
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|txt|   |string  |    |
|st| 响应状态  |integer(int32)  | integer(int32)   |
|msg| 响应消息  |string  |    |
|body| 响应内容  |下单响应  | 下单响应   |



**schema属性说明**




**下单响应**

| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | ------------------|--------|----------- |
|pplanno | MES单号   |string  |    |

**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |接口响应«下单响应»|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
# 大货单接口

## 外贸大货下单

**接口描述**:一个生产计划单下多个PO

**接口地址**:`/out/pp/create/model1/{token}`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`


**请求示例**：
```json
{
	"customerId": "SP-030239",
	"customerName": "美国HM加州",
	"docList": [
		{
			"craftdetail": "",
			"img1Name": "",
			"img1Url": "http://**/doc/tec/file",
			"img2Name": "",
			"img2Url": "http://**/doc/tec/file",
			"img3Name": "",
			"img3Url": "http://**/doc/tec/file",
			"pdf1Name": "",
			"pdf1Url": "http://**/doc/tec/file",
			"pdf2Name": "",
			"pdf2Url": "http://**/doc/tec/file",
			"pdf3Name": "",
			"pdf3Url": "http://**/doc/tec/file",
			"pdptcd": "",
			"pdptnm": "",
			"wctycd": "",
			"wctynm": ""
		}
	],
	"dueDate": "2024-10-23",
	"lineList": [
		{
			"bomList": [
				{
					"cut_width": 11,
					"cut_wstrat": 20,
					"mainMterial": false,
					"materialBatchno": "PC-2024-312",
					"materialClrnm": "红色03",
					"materialColor": "HD-CC-03",
					"materialCom": "12织",
					"materialId": "DT-34003",
					"materialName": "DT-34003",
					"materialTypeId": "JND-023",
					"materialTypeName": "真织面料",
					"materialUsage": "包装",
					"thirdUid": "012c3812-2e3b-4f5a-bffa-cce5180c8178",
					"tolConsu": 123.21,
					"uniConsu": 0.47,
					"unitcd": "kg",
					"unitnm": "千克",
					"width": 1.47
				}
			],
			"itemList": [
				{
					"deliveryAddress": "美国HM加州",
					"deliveryCountry": "美国",
					"devliveryWay": "海运",
					"itemQty": 23,
					"orderDno": "PO-0023XF",
					"sizeNo": "XL"
				}
			],
			"mainMcolor": "藏青-31",
			"mainMid": "QZ-230231",
			"mainMname": "QZ-230231",
			"mainSpec": "DG-0231",
			"remark": "藏青-31",
			"skuName": "春秋-34003",
			"skuSpec": "春秋-34003",
			"thirdUid": "件",
			"unitnm": "件"
		}
	],
	"mrchds": "李嘉",
	"orderDate": "2024-10-23",
	"orderNo": "SO-0023XF",
	"orderPno": "PO-0023XF",
	"ppFrom": "ERP",
	"prodId": "FZ-NDQ",
	"prodName": "女短裙",
	"remark": "在ST-春季-01款上，口袋向下0.5cm",
	"season": "春季",
	"stylId": "ST-春季-02",
	"stylName": "ST-春季-02",
	"thirdUid": "8dc9d027-5465-4f07-917f-9d5391bf848a",
	"wkspcd": "李嘉"
}
```


**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|modelReqeust| modelReqeust  | body | true |外贸大货下单请求  | 外贸大货下单请求   |
|token| 请求令牌  | path | true |string  |    |
|status| 订单状态(新单|审核通过|产前完成)  | query | false |string  |    |

**schema属性说明**



**外贸大货下单请求**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|customerId| 客户编号  | body | false |string  |    |
|customerName| 客户名称  | body | false |string  |    |
|docList| 款式文件  | body | false |array  | 工艺文件   |
|dueDate| 交货日期  | body | true |string  |    |
|lineList| 订单明细  | body | true |array  | 外贸大货订单明细   |
|mrchds| 业务员  | body | false |string  |    |
|orderDate| 下单日期  | body | false |string  |    |
|orderNo| 销售订单号  | body | false |string  |    |
|orderPno| 生产计划号  | body | true |string  |    |
|ppFrom| 订单来源系统  | body | true |string  |    |
|prodId| 品类编号  | body | true |string  |    |
|prodName| 品类名称  | body | false |string  |    |
|remark| 订单备注  | body | false |string  |    |
|season| 款式季节  | body | false |string  |    |
|stylId| 款式编号  | body | true |string  |    |
|stylName| 款式名称  | body | false |string  |    |
|thirdUid| 订单唯一主键(来源系统)  | body | true |string  |    |
|wkspcd| 指定车间  | body | false |string  |    |

**工艺文件**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|craftdetail| 工艺描述  | body | false |string  |    |
|img1Name| 工艺图1名称  | body | false |string  |    |
|img1Url| 工艺图1下载地址  | body | false |string  |    |
|img2Name| 工艺图2名称  | body | false |string  |    |
|img2Url| 工艺图2下载地址  | body | false |string  |    |
|img3Name| 工艺图3名称  | body | false |string  |    |
|img3Url| 工艺图3下载地址  | body | false |string  |    |
|pdf1Name| 工艺文件1名称  | body | false |string  |    |
|pdf1Url| 工艺文件1下载地址  | body | false |string  |    |
|pdf2Name| 工艺文件2名称  | body | false |string  |    |
|pdf2Url| 工艺文件2下载地址  | body | false |string  |    |
|pdf3Name| 工艺文件3名称  | body | false |string  |    |
|pdf3Url| 工艺文件3下载地址  | body | false |string  |    |
|pdptcd| 部件编号  | body | false |string  |    |
|pdptnm| 部件名称  | body | false |string  |    |
|wctycd| 工段编号  | body | false |string  |    |
|wctynm| 工段名称  | body | false |string  |    |

**外贸大货订单明细**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|bomList| 物料清单  | body | false |array  | 外贸大货物料清单   |
|itemList| 尺码清单  | body | true |array  | 外贸大货尺码清单   |
|mainMcolor| 主料(面料)颜色  | body | true |string  |    |
|mainMid| 主料(面料)编号  | body | true |string  |    |
|mainMname| 主料(面料)名称  | body | false |string  |    |
|mainSpec| 主料(面料)规格  | body | false |string  |    |
|remark| 备注  | body | false |string  |    |
|skuName| 产品名称  | body | false |string  |    |
|skuSpec| 产品规格  | body | false |string  |    |
|thirdUid| 第三方主键  | body | false |string  |    |
|unitnm| 单位  | body | false |string  |    |

**外贸大货物料清单**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|cut_width| 裁剪宽度  | body | false |string  |    |
|cut_wstrat| 裁剪用量  | body | false |string  |    |
|mainMterial| 主料(非常备)/辅料(常备)  | body | true |boolean  |    |
|materialBatchno| 物料批次  | body | false |string  |    |
|materialClrnm| 物料颜色名称  | body | false |string  |    |
|materialColor| 物料颜色编号  | body | false |string  |    |
|materialCom| 物料成份  | body | false |string  |    |
|materialId| 物料编号  | body | true |string  |    |
|materialName| 物料名称  | body | false |string  |    |
|materialTypeId| 物料分类编号  | body | false |string  |    |
|materialTypeName| 物料分类名称  | body | false |string  |    |
|materialUsage| 物料用途  | body | false |string  |    |
|thirdUid| 每三方系统唯一主键  | body | false |string  |    |
|tolConsu| 总用量  | body | false |number  |    |
|uniConsu| 物料单耗  | body | false |number  |    |
|unitcd| 单位编号  | body | false |string  |    |
|unitnm| 单位名称  | body | false |string  |    |
|width| 物料宽度  | body | false |number  |    |

**外贸大货尺码清单**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|deliveryAddress| 发货地址  | body | false |string  |    |
|deliveryCountry| 发货国家  | body | false |string  |    |
|devliveryWay| 发货方式  | body | false |string  |    |
|itemQty| 数量  | body | true |integer(int32)  |    |
|orderDno| PO单号  | body | true |string  |    |
|sizeNo| 尺码  | body | true |string  |    |

**响应示例**:

```json
{
	"txt": "",
	"st": "0成功，-1失败",
	"msg": "Success",
	"body": {
		"pplanno": "PP24060078"
	}
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|txt|   |string  |    |
|st| 响应状态  |integer(int32)  | integer(int32)   |
|msg| 响应消息  |string  |    |
|body| 响应内容  |下单响应  | 下单响应   |



**schema属性说明**




**下单响应**

| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | ------------------|--------|----------- |
|pplanno | MES单号   |string  |    |

**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |接口响应«下单响应»|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 国内大货下单


**接口描述**:


**接口地址**:`/out/pp/create/model2/{token}`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`


**请求示例**：
```json
{
	"customerId": "SP-030239",
	"customerName": "美国HM加州",
	"dueDate": "2024-10-23",
	"lineList": [
		{
			"bomList": [
				{
					"cut_width": 11,
					"cut_wstrat": 20,
					"mainMterial": false,
					"materialBatchno": "PC-2024-312",
					"materialClrnm": "红色03",
					"materialColor": "HD-CC-03",
					"materialCom": "12织",
					"materialId": "DT-34003",
					"materialName": "DT-34003",
					"materialTypeId": "JND-023",
					"materialTypeName": "真织面料",
					"materialUsage": "包装",
					"thirdUid": "012c3812-2e3b-4f5a-bffa-cce5180c8178",
					"tolConsu": 123.21,
					"uniConsu": 0.47,
					"unitcd": "kg",
					"unitnm": "千克",
					"width": 1.47
				}
			],
			"itemList": [
				{
					"itemQty": 23,
					"sizeNo": "XL"
				}
			],
			"mainMcolor": "藏青-31",
			"mainMid": "QZ-230231",
			"mainMname": "QZ-230231",
			"mainSpec": "DG-0231",
			"prodId": "FZ-NDQ",
			"prodName": "女短裙",
			"remark": "藏青-31",
			"skuName": "春秋-34003",
			"skuSpec": "春秋-34003",
			"stylId": "ST-春季-02",
			"stylName": "ST-春季-02",
			"tecList": [
				{
					"craftdetail": "",
					"img1Name": "",
					"img1Url": "http://**/doc/tec/file",
					"img2Name": "",
					"img2Url": "http://**/doc/tec/file",
					"img3Name": "",
					"img3Url": "http://**/doc/tec/file",
					"pdf1Name": "",
					"pdf1Url": "http://**/doc/tec/file",
					"pdf2Name": "",
					"pdf2Url": "http://**/doc/tec/file",
					"pdf3Name": "",
					"pdf3Url": "http://**/doc/tec/file",
					"pdptcd": "",
					"pdptnm": "",
					"wctycd": "",
					"wctynm": ""
				}
			],
			"unitnm": "件"
		}
	],
	"mrchds": "李嘉",
	"orderDate": "2024-10-23",
	"orderNo": "SO-0023XF",
	"orderPno": "PO-0023XF",
	"ppFrom": "ERP",
	"remark": "在ST-春季-01款上，口袋向下0.5cm",
	"thirdUid": "8dc9d027-5465-4f07-917f-9d5391bf848a",
	"wkspcd": "李嘉"
}
```


**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|modelReqeust| modelReqeust  | body | true |国内大货下单请求  | 国内大货下单请求   |
|token| 请求令牌  | path | true |string  |    |
|status| 订单状态(新单|审核通过|产前完成)  | query | false |string  |    |

**schema属性说明**



**国内大货下单请求**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|customerId| 客户编号  | body | false |string  |    |
|customerName| 客户名称  | body | false |string  |    |
|dueDate| 交货日期  | body | true |string  |    |
|lineList| 订单明细  | body | true |array  | 内销大货订单明细   |
|mrchds| 业务员  | body | false |string  |    |
|orderDate| 下单日期  | body | false |string  |    |
|orderNo| 销售订单号  | body | false |string  |    |
|orderPno| 生产计划号  | body | true |string  |    |
|ppFrom| 订单来源系统  | body | true |string  |    |
|remark| 订单备注  | body | false |string  |    |
|thirdUid| 订单唯一主键(来源系统)  | body | true |string  |    |
|wkspcd| 指定车间  | body | false |string  |    |

**内销大货订单明细**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|bomList| 物料清单  | body | false |array  | 外贸大货物料清单   |
|itemList| 尺码清单  | body | true |array  | 内销大货尺码清单   |
|mainMcolor| 主料(面料)颜色  | body | true |string  |    |
|mainMid| 主料(面料)编号  | body | true |string  |    |
|mainMname| 主料(面料)名称  | body | false |string  |    |
|mainSpec| 主料(面料)规格  | body | false |string  |    |
|prodId| 品类编号  | body | true |string  |    |
|prodName| 品类名称  | body | false |string  |    |
|remark| 备注  | body | false |string  |    |
|skuName| 产品名称  | body | false |string  |    |
|skuSpec| 产品规格  | body | false |string  |    |
|stylId| 款式编号  | body | true |string  |    |
|stylName| 款式名称  | body | false |string  |    |
|tecList| 工艺清单  | body | true |array  | 工艺文件   |
|unitnm| 单位  | body | false |string  |    |

**外贸大货物料清单**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|cut_width| 裁剪宽度  | body | false |string  |    |
|cut_wstrat| 裁剪用量  | body | false |string  |    |
|mainMterial| 主料(非常备)/辅料(常备)  | body | true |boolean  |    |
|materialBatchno| 物料批次  | body | false |string  |    |
|materialClrnm| 物料颜色名称  | body | false |string  |    |
|materialColor| 物料颜色编号  | body | false |string  |    |
|materialCom| 物料成份  | body | false |string  |    |
|materialId| 物料编号  | body | true |string  |    |
|materialName| 物料名称  | body | false |string  |    |
|materialTypeId| 物料分类编号  | body | false |string  |    |
|materialTypeName| 物料分类名称  | body | false |string  |    |
|materialUsage| 物料用途  | body | false |string  |    |
|thirdUid| 每三方系统唯一主键  | body | false |string  |    |
|tolConsu| 总用量  | body | false |number  |    |
|uniConsu| 物料单耗  | body | false |number  |    |
|unitcd| 单位编号  | body | false |string  |    |
|unitnm| 单位名称  | body | false |string  |    |
|width| 物料宽度  | body | false |number  |    |

**内销大货尺码清单**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|itemQty| 数量  | body | true |integer(int32)  |    |
|sizeNo| 尺码  | body | true |string  |    |

**工艺文件**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|craftdetail| 工艺描述  | body | false |string  |    |
|img1Name| 工艺图1名称  | body | false |string  |    |
|img1Url| 工艺图1下载地址  | body | false |string  |    |
|img2Name| 工艺图2名称  | body | false |string  |    |
|img2Url| 工艺图2下载地址  | body | false |string  |    |
|img3Name| 工艺图3名称  | body | false |string  |    |
|img3Url| 工艺图3下载地址  | body | false |string  |    |
|pdf1Name| 工艺文件1名称  | body | false |string  |    |
|pdf1Url| 工艺文件1下载地址  | body | false |string  |    |
|pdf2Name| 工艺文件2名称  | body | false |string  |    |
|pdf2Url| 工艺文件2下载地址  | body | false |string  |    |
|pdf3Name| 工艺文件3名称  | body | false |string  |    |
|pdf3Url| 工艺文件3下载地址  | body | false |string  |    |
|pdptcd| 部件编号  | body | false |string  |    |
|pdptnm| 部件名称  | body | false |string  |    |
|wctycd| 工段编号  | body | false |string  |    |
|wctynm| 工段名称  | body | false |string  |    |

**响应示例**:

```json
{
	"txt": "",
	"st": "0成功，-1失败",
	"msg": "Success",
	"body": {
		"pplanno": "PP24060078"
	}
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|txt|   |string  |    |
|st| 响应状态  |integer(int32)  | integer(int32)   |
|msg| 响应消息  |string  |    |
|body| 响应内容  |下单响应  | 下单响应   |



**schema属性说明**




**下单响应**

| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | ------------------|--------|----------- |
|pplanno | MES单号   |string  |    |

**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |接口响应«下单响应»|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 生产订单撤销


**接口描述**:


**接口地址**:`/out/pp/revoke/model1/{token}`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`


**请求示例**：
```json
{
	"modifyBy": "李嘉",
	"modifyReason": "订单下重复",
	"orderPno": "PO-0023XF",
	"ppFrom": "ERP",
	"thirdUid": "8dc9d027-5465-4f07-917f-9d5391bf848a"
}
```


**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|revokeRequest| revokeRequest  | body | true |订单撤销请求  | 订单撤销请求   |
|token| 请求令牌  | path | true |string  |    |

**schema属性说明**



**订单撤销请求**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|modifyBy| 撤销操作人  | body | true |string  |    |
|modifyReason| 撤销原因  | body | false |string  |    |
|orderPno| 生产计划号  | body | false |string  |    |
|ppFrom| 接口来源  | body | true |string  |    |
|thirdUid| 订单唯一主键(来源系统)  | body | true |string  |    |

**响应示例**:

```json
{
	"txt": "",
	"st": "0成功，-1失败",
	"msg": "Success",
	"body": ""
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|txt|   |string  |    |
|st| 响应状态  |integer(int32)  | integer(int32)   |
|msg| 响应消息  |string  |    |
|body| 响应内容  |string  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |接口响应«string»|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
# 订单接口

## 生产订单报工

**接口描述**:WMS->ERP，ERP系统做为服务端

**接口地址**:`/pp/demo/report/request`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`


**请求示例**：
```json
{
	"itemQty": 23,
	"mainMcolor": "藏青-31",
	"mainMid": "QZ-230231",
	"mainMname": "QZ-230231",
	"mainSpec": "DG-0231",
	"orderNo": "SO-0023XF",
	"orderPno": "PO-0023XF",
	"prodId": "FZ-NDQ",
	"prodName": "女短裙",
	"reportDate": "2024-10-23",
	"sizeNo": "XL",
	"skuSpec": "春秋-34003",
	"stylId": "ST-春季-02",
	"stylName": "ST-春季-02",
	"thirdUid": "8dc9d027-5465-4f07-917f-9d5391bf848a"
}
```


**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|request| request  | body | true |报工清单  | 报工清单   |

**schema属性说明**



**报工清单**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|itemQty| 数量  | body | true |number  |    |
|mainMcolor| 主料(面料)颜色  | body | false |string  |    |
|mainMid| 主料(面料)编号  | body | true |string  |    |
|mainMname| 主料(面料)名称  | body | false |string  |    |
|mainSpec| 主料(面料)规格  | body | false |string  |    |
|orderNo| 销售订单号  | body | true |string  |    |
|orderPno| 生产计划号  | body | true |string  |    |
|prodId| 品类编号  | body | true |string  |    |
|prodName| 品类名称  | body | false |string  |    |
|reportDate| 报工日期  | body | false |string(date-time)  |    |
|sizeNo| 尺码  | body | true |string  |    |
|skuSpec| 产品规格  | body | false |string  |    |
|stylId| 款式编号  | body | true |string  |    |
|stylName| 款式名称  | body | false |string  |    |
|thirdUid| 订单唯一主键(来源系统)  | body | true |string  |    |

**响应示例**:

```json
{
	"txt": "",
	"st": "0成功，-1失败",
	"msg": "Success",
	"body": ""
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|txt|   |string  |    |
|st| 响应状态  |integer(int32)  | integer(int32)   |
|msg| 响应消息  |string  |    |
|body| 响应内容  |string  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |接口响应«string»|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
